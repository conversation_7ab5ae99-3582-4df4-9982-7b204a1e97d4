{"name": "metro-resolver", "version": "0.76.9", "description": "🚇 Implementation of Metro's resolution logic.", "main": "src", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "engines": {"node": ">=16"}}