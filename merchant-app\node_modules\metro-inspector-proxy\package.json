{"name": "metro-inspector-proxy", "version": "0.76.9", "description": "🚇 Inspector proxy for React Native and dev tools integration.", "main": "src/index.js", "bin": "src/cli.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "keywords": ["metro"], "license": "MIT", "dependencies": {"connect": "^3.6.5", "debug": "^2.2.0", "ws": "^7.5.1", "yargs": "^17.6.2", "node-fetch": "^2.2.0"}, "engines": {"node": ">=16"}}